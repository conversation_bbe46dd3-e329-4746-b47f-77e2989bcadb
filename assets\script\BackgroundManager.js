cc.Class({
    extends: cc.Component,

    properties: {
        backgroundSprite: {
            default: null,
            type: cc.SpriteFrame,
            tooltip: "背景图片"
        },
        scrollSpeed: {
            default: 100,
            tooltip: "背景滚动速度"
        }
    },

    onLoad() {
        // 获取屏幕宽度
        this.screenWidth = cc.winSize.width;

        // 创建背景节点数组（用于无缝滚动）
        this.backgroundNodes = [];

        // 初始化背景
        this.initBackgrounds();
    },

    // 初始化背景系统
    initBackgrounds() {
        // 创建两个背景节点用于无缝滚动
        for (let i = 0; i < 2; i++) {
            let bgNode = new cc.Node(`Background_${i}`);
            let sprite = bgNode.addComponent(cc.Sprite);

            // 设置背景图片
            if (this.backgroundSprite) {
                sprite.spriteFrame = this.backgroundSprite;
                sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            }

            // 设置节点属性
            bgNode.parent = this.node;
            bgNode.zIndex = -1; // 确保背景在最底层

            // 设置大小以覆盖整个屏幕
            bgNode.width = this.screenWidth;
            bgNode.height = cc.winSize.height;

            // 设置位置：两个背景节点并排放置
            bgNode.x = i * this.screenWidth;
            bgNode.y = 0;

            this.backgroundNodes.push(bgNode);
        }

        // console.log("简化背景管理器初始化完成");
    },

    start() {},

    update(dt) {
        // 滚动背景
        this.scrollBackgrounds(dt);
    },

    // 滚动背景
    scrollBackgrounds(dt) {
        this.backgroundNodes.forEach(bgNode => {
            // 向左移动
            bgNode.x -= this.scrollSpeed * dt;

            // 如果背景完全移出屏幕左侧，将其移动到最右侧
            if (bgNode.x <= -this.screenWidth) {
                // 将当前节点移动到最右边
                bgNode.x += this.screenWidth * 2;
            }
        });
    },

    // 设置滚动速度（由GameManager调用）
    setScrollSpeed(speed) {
        this.scrollSpeed = speed;
    },

    // 重置背景系统
    resetBackgrounds() {
        // 重置背景位置
        this.backgroundNodes.forEach((bgNode, index) => {
            bgNode.x = index * this.screenWidth;
        });

        // console.log("背景系统已重置");
    }
});
